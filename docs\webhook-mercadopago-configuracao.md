# Configuração do Webhook Mercado Pago

## ✅ Status da Configuração

O webhook do Mercado Pago foi configurado com sucesso! Aqui estão os detalhes:

### Credenciais Configuradas

- **Access Token**: `TEST-3509556019068409-072017-4e0126f5af5077bcbbb6f1055e5d4f19-247350285`
- **Public Key**: `TEST-ae38256c-b35b-473b-b4f2-e8d4a81c242d`
- **Modo**: Sandbox (teste)

### URL do Webhook

```
POST /webhook/mercadopago
```

**URLs para configurar no Mercado Pago:**

🧪 **TESTE (Sandbox):**
```
https://f4-fisio.sevalla.app/webhook/mercadopago
```

🚀 **PRODUÇÃO:**
```
https://f4-fisio.sevalla.app/webhook/mercadopago
```

**URL de Redirecionamento (checkout):**
```
https://f4-fisio.sevalla.app/checkout
```

> **Importante**: Use a mesma URL de webhook para teste e produção. O sistema detecta automaticamente o ambiente baseado nas credenciais configuradas.

**Desenvolvimento local**: `http://localhost:8000/webhook/mercadopago`

## 🔧 Arquivos Configurados

### 1. Variáveis de Ambiente (.env)
```env
MERCADOPAGO_ACCESS_TOKEN=TEST-3509556019068409-072017-4e0126f5af5077bcbbb6f1055e5d4f19-247350285
MERCADOPAGO_PUBLIC_KEY=TEST-ae38256c-b35b-473b-b4f2-e8d4a81c242d
MERCADOPAGO_SANDBOX=true
# Webhook Secret - Teste: ed2e5532a1d5e491384a88887ffdb5961cb157e4ea21be84606d3d5c2d1c78a4
# Webhook Secret - Prod: ed2e5532a1d5e491384a88887ffdb5961cb157e4ea21be84606d3d5c2d1c78a4
MERCADOPAGO_WEBHOOK_SECRET=ed2e5532a1d5e491384a88887ffdb5961cb157e4ea21be84606d3d5c2d1c78a4
```

### 2. Configuração de Serviços (config/services.php)
```php
'mercadopago' => [
    'access_token' => env('MERCADOPAGO_ACCESS_TOKEN'),
    'public_key' => env('MERCADOPAGO_PUBLIC_KEY'),
    'sandbox' => env('MERCADOPAGO_SANDBOX', true),
    'simulate' => env('MERCADOPAGO_SIMULATE', env('APP_ENV') === 'local'),
    'webhook_secret' => env('MERCADOPAGO_WEBHOOK_SECRET'),
],
```

### 3. Middleware CSRF (bootstrap/app.php)
O webhook está excluído da verificação CSRF:
```php
$middleware->validateCsrfTokens(except: [
    '/api/estabelecimentos/buscar',
    '/api/estabelecimento/*/contato',
    '/webhook/mercadopago', // ✅ Configurado
]);
```

### 4. Rotas (routes/web.php)
```php
// Webhook do Mercado Pago
Route::post('/webhook/mercadopago', [App\Http\Controllers\MercadoPagoWebhookController::class, 'handle'])
    ->name('mercadopago.webhook');

// Rota de checkout (redirecionamento após pagamento)
Route::get('/checkout', function () {
    return redirect()->route('paciente.pagamentos.success');
})->name('checkout');
```

## 🧪 Testes Realizados

### Teste 1: Webhook Básico
```bash
curl -X POST http://localhost:8000/webhook/mercadopago \
  -H "Content-Type: application/json" \
  -d '{"type":"payment","data":{"id":"123456789"}}'
```
**Resultado**: ✅ `{"status":"ok"}`

### Teste 2: Webhook Completo
```bash
curl -X POST http://localhost:8000/webhook/mercadopago \
  -H "Content-Type: application/json" \
  -d '{
    "id":12345,
    "live_mode":false,
    "type":"payment",
    "date_created":"2024-01-15T10:00:00.000-04:00",
    "application_id":"123456",
    "user_id":"123456",
    "version":1,
    "api_version":"v1",
    "action":"payment.updated",
    "data":{"id":"TEST_123456"}
  }'
```
**Resultado**: ✅ `{"status":"ok","request_id":"webhook_68952514d4eab","processing_time_ms":327.65}`

## 📊 Sistema de Logs Melhorado

### Logs Estruturados com Emojis
O sistema agora possui logs estruturados e organizados para facilitar o monitoramento em produção:

- 🔔 **Webhook recebido**: Log inicial com todos os dados da requisição
- 🔍 **Buscando informações**: Logs de consultas ao MP e banco de dados
- 💳 **Processando pagamento**: Logs específicos do processamento
- 🔄 **Status mapeado**: Logs de mudança de status
- 💰 **Pagamento aprovado**: Logs de pagamentos bem-sucedidos
- 🤝 **Verificando afiliado**: Logs de comissões de afiliados
- ✅ **Sucesso**: Logs de conclusão bem-sucedida
- ⚠️ **Avisos**: Logs de situações que precisam atenção
- ❌ **Erros**: Logs de erros com stack trace completo

### Request ID para Rastreamento
Cada webhook recebe um `request_id` único que permite rastrear todo o fluxo:
```
request_id: webhook_68952514d4eab
```

### Tempo de Processamento
O sistema mede e registra o tempo de processamento de cada webhook:
```
processing_time_ms: 327.65
```

### Exemplo de Log Completo
```
[2025-01-07 15:30:45] local.INFO: 🔔 [WEBHOOK] Mercado Pago - Notificação recebida {
  "request_id": "webhook_68952514d4eab",
  "timestamp": "2025-01-07T15:30:45.123Z",
  "ip": "127.0.0.1",
  "type": "payment",
  "action": "payment.updated",
  "payment_id": "TEST_123456",
  "live_mode": false
}
```

## 📋 Funcionalidades Implementadas

### MercadoPagoWebhookController
- ✅ Recebe notificações do Mercado Pago
- ✅ **Valida assinatura do webhook** (segurança)
- ✅ Processa pagamentos aprovados/rejeitados
- ✅ Atualiza status dos pagamentos no banco
- ✅ Confirma comissões de afiliados
- ✅ Log detalhado de todas as operações
- ✅ Request ID único para rastreamento
- ✅ Medição de tempo de processamento

### Mapeamento de Status
- `approved` → `pago`
- `pending` / `in_process` → `pendente`
- `rejected` / `cancelled` → `falhou`

### Métodos de Pagamento Suportados
- PIX
- Cartão de Crédito (Visa, Master, Amex, Elo, Hipercard)
- Cartão de Débito
- Boleto

## 🚀 Próximos Passos

### 1. Configurar no Painel do Mercado Pago
1. Acesse: https://www.mercadopago.com.br/developers/panel/app
2. Vá em "Webhooks" ou "Notificações"
3. **Adicione a URL**: `https://f4-fisio.sevalla.app/webhook/mercadopago`
4. **Selecione os eventos**:
   - ✅ **Pagamentos** (obrigatório)
   - ✅ **Planos e assinaturas** (recomendado)
5. **Configure a assinatura secreta**: `ed2e5532a1d5e491384a88887ffdb5961cb157e4ea21be84606d3d5c2d1c78a4`

### 2. Testar em Produção
1. Altere as credenciais para produção no `.env`
2. Configure `MERCADOPAGO_SANDBOX=false`
3. Teste com pagamentos reais

### 3. Monitoramento
- Verifique os logs em `storage/logs/laravel.log`
- Monitore a tabela `pagamentos` no banco de dados
- Acompanhe as comissões de afiliados

## 🔍 Debugging e Monitoramento

### Monitorar Logs em Tempo Real
```bash
# Todos os logs
tail -f storage/logs/laravel.log

# Apenas logs do webhook (com filtro)
tail -f storage/logs/laravel.log | grep "WEBHOOK"

# Logs de pagamentos aprovados
tail -f storage/logs/laravel.log | grep "💰.*WEBHOOK.*Pagamento aprovado"

# Logs de erros do webhook
tail -f storage/logs/laravel.log | grep "❌.*WEBHOOK"
```

### Testar Webhook Localmente
```bash
# Teste básico
curl -X POST http://localhost:8000/webhook/mercadopago \
  -H "Content-Type: application/json" \
  -d '{"type":"payment","data":{"id":"123456"}}'

# Teste completo (simula webhook real do MP)
curl -X POST http://localhost:8000/webhook/mercadopago \
  -H "Content-Type: application/json" \
  -d '{
    "id":12345,
    "live_mode":false,
    "type":"payment",
    "date_created":"2024-01-15T10:00:00.000-04:00",
    "application_id":"123456",
    "user_id":"123456",
    "version":1,
    "api_version":"v1",
    "action":"payment.updated",
    "data":{"id":"TEST_123456"}
  }'

# Verificar resposta (deve incluir request_id e processing_time)
# Exemplo: {"status":"ok","request_id":"webhook_123","processing_time_ms":327.65}
```

### Verificar Configuração
```bash
# Via artisan tinker
php artisan tinker
>>> config('services.mercadopago')

# Verificar se webhook está acessível
curl -I http://localhost:8000/webhook/mercadopago
# Deve retornar: HTTP/1.1 405 Method Not Allowed (GET não permitido, apenas POST)
```

### Comandos Úteis para Produção
```bash
# Verificar últimos 100 webhooks processados
tail -n 1000 storage/logs/laravel.log | grep "WEBHOOK.*recebida" | tail -100

# Contar webhooks por status nas últimas 24h
grep "$(date '+%Y-%m-%d')" storage/logs/laravel.log | grep "WEBHOOK" | grep -c "✅"
grep "$(date '+%Y-%m-%d')" storage/logs/laravel.log | grep "WEBHOOK" | grep -c "❌"

# Verificar tempo médio de processamento
grep "$(date '+%Y-%m-%d')" storage/logs/laravel.log | grep "processing_time_ms" | grep -o '"processing_time_ms":[0-9.]*' | cut -d: -f2
```

## 📞 Suporte

Se houver problemas:
1. Verifique os logs do Laravel
2. Confirme se as credenciais estão corretas
3. Teste o webhook localmente primeiro
4. Verifique se a URL está acessível publicamente

---

**Status**: ✅ Configurado e Funcionando
**Data**: 2025-01-07
**Versão**: 1.0

## 📋 Funcionalidades Implementadas

### Controller: MercadoPagoWebhookController
- ✅ Recebe notificações do Mercado Pago
- ✅ Processa pagamentos aprovados/rejeitados
- ✅ Atualiza status no banco de dados
- ✅ Confirma comissões de afiliados
- ✅ Log detalhado de todas as operações

### Service: MercadoPagoService
- ✅ Integração com API do Mercado Pago
- ✅ Suporte a modo sandbox
- ✅ Simulador para desenvolvimento
- ✅ Métodos para buscar informações de pagamento

### Mapeamento de Status
```php
'approved' => 'pago'
'pending' => 'pendente'
'in_process' => 'pendente'
'rejected' => 'falhou'
'cancelled' => 'falhou'
```

## 🚀 Próximos Passos

### Para Produção
1. **Configurar URL pública**: Substitua `localhost:8000` pela URL real do servidor
2. **Credenciais de produção**: Substitua as credenciais TEST por credenciais reais
3. **Configurar webhook no Mercado Pago**: 
   - Acesse o painel do Mercado Pago
   - Configure a URL: `https://seudominio.com/webhook/mercadopago`
   - Selecione eventos: `payment`

### Para Desenvolvimento
1. **Usar ngrok** para expor localhost:
   ```bash
   ngrok http 8000
   ```
2. **Configurar URL temporária** no painel do Mercado Pago

## 🔍 Como Testar

### 1. Verificar se o servidor está rodando
```bash
composer run dev
```

### 2. Testar webhook manualmente
```bash
curl -X POST http://localhost:8000/webhook/mercadopago \
  -H "Content-Type: application/json" \
  -d '{"type":"payment","data":{"id":"123456"}}'
```

### 3. Verificar logs
```bash
tail -f storage/logs/laravel.log
```

### 4. Simular pagamento completo
Use o simulador já implementado em `/mercadopago/simulator/`

## 📝 Logs e Monitoramento

O sistema registra logs detalhados em:
- `storage/logs/laravel.log`
- Logs específicos com tag `Webhook Mercado Pago recebido`
- Logs de erro com stack trace completo

## ⚠️ Importante

- O webhook está configurado para **modo sandbox** (teste)
- As credenciais fornecidas são de **teste**
- Para produção, será necessário obter credenciais reais do Mercado Pago
- O sistema já suporta tanto pagamentos reais quanto simulados

## 🎯 Resumo

✅ **Webhook configurado e funcionando**  
✅ **Credenciais de teste configuradas**  
✅ **Processamento de pagamentos implementado**  
✅ **Sistema de logs funcionando**  
✅ **Integração com afiliados funcionando**  
✅ **Testes realizados com sucesso**

O sistema está pronto para receber webhooks do Mercado Pago!
