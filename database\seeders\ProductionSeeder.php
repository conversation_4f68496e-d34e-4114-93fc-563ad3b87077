<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Plano;
use Illuminate\Database\Seeder;

class ProductionSeeder extends Seeder
{
    /**
     * Seed the application's database for production.
     */
    public function run(): void
    {
        // Criar usuário admin padrão se não existir
        if (!User::where('email', '<EMAIL>')->exists()) {
            User::create([
                'name' => 'Administrador F4 Fisio',
                'email' => '<EMAIL>',
                'password' => bcrypt('admin123'),
                'role' => 'admin',
                'active' => true,
                'has_subscription' => true,
            ]);
        }

        // Criar planos padrão se não existirem
        if (Plano::count() === 0) {
            Plano::create([
                'name' => 'Plano Busca',
                'description' => 'Encontre fisioterapeutas, farmácias e dentistas próximos a você',
                'price' => 14.80,
                'sessions_per_month' => 0,
                'session_duration' => 0,
                'included_services' => [
                    'Busca ilimitada de profissionais',
                    'Filtros avançados por especialidade',
                    'Visualização de avaliações',
                    'Informações de contato',
                    'Localização no mapa',
                    'Horários de funcionamento',
                    'Suporte por chat'
                ],
                'benefits' => [
                    'Busca ilimitada de profissionais',
                    'Filtros avançados',
                    'Suporte por chat'
                ],
                'active' => true,
            ]);

            Plano::create([
                'name' => 'Plano Pessoal',
                'description' => 'Atendimento fisioterapêutico domiciliar personalizado',
                'price' => 180.00,
                'sessions_per_month' => 1,
                'session_duration' => 60,
                'included_services' => [
                    'Todos os recursos do Plano Busca',
                    'Agendamento de consultas domiciliares',
                    'Fisioterapeutas qualificados',
                    'Atendimento personalizado',
                    'Relatórios de avaliação',
                    'Suporte telefônico',
                    'Cancelamento flexível',
                    'Histórico médico digital'
                ],
                'benefits' => [
                    'Atendimento domiciliar',
                    'Fisioterapeutas qualificados',
                    'Relatórios de avaliação',
                    'Suporte telefônico'
                ],
                'active' => true,
            ]);

            Plano::create([
                'name' => 'Plano Empresarial',
                'description' => 'Solução completa de fisioterapia para sua empresa',
                'price' => 640.00,
                'sessions_per_month' => 4,
                'session_duration' => 60,
                'included_services' => [
                    'Todos os recursos do Plano Pessoal',
                    'Atendimento para até 20 funcionários',
                    'Gestão centralizada',
                    'Relatórios empresariais',
                    'Programa de prevenção',
                    'Treinamentos ergonômicos',
                    'Descontos em consultas extras',
                    'Dashboard administrativo'
                ],
                'benefits' => [
                    'Atendimento empresarial',
                    'Gestão centralizada',
                    'Relatórios empresariais',
                    'Dashboard administrativo'
                ],
                'active' => true,
            ]);

            Plano::create([
                'name' => 'Plano Empresarial',
                'description' => 'Solução personalizada para empresas e grupos',
                'price' => 0.00, // Preço sob consulta
                'sessions_per_month' => null,
                'active' => true,
                'features' => [
                    'Sessões ilimitadas',
                    'Múltiplos usuários',
                    'Relatórios avançados',
                    'Suporte dedicado',
                    'Integração personalizada'
                ]
            ]);
        }

        $this->command->info('✅ Dados de produção criados com sucesso!');
        $this->command->info('🔑 Credenciais de acesso:');
        $this->command->info('   Admin: <EMAIL> / admin123');
        $this->command->info('📋 Planos criados:');
        $this->command->info('   - Plano Mensal (R$ 64,00)');
        $this->command->info('   - Sessão Avulsa (R$ 80,00)');
        $this->command->info('   - Plano Empresarial (Sob consulta)');
    }
}
