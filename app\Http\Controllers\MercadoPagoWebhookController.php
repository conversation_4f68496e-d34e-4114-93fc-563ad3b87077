<?php

namespace App\Http\Controllers;

use App\Models\Pagamento;
use App\Models\VendaAfiliado;
use App\Services\MercadoPagoService;
use App\Services\AffiliateTrackingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class MercadoPagoWebhookController extends Controller
{
    protected $mercadoPagoService;

    public function __construct(MercadoPagoService $mercadoPagoService)
    {
        $this->mercadoPagoService = $mercadoPagoService;
    }

    /**
     * Handle Mercado Pago webhook notifications
     */
    public function handle(Request $request)
    {
        $startTime = microtime(true);
        $requestId = uniqid('webhook_');

        try {
            Log::info('🔔 [WEBHOOK] Mercado Pago - Notificação recebida', [
                'request_id' => $requestId,
                'timestamp' => now()->toISOString(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'type' => $request->input('type'),
                'action' => $request->input('action'),
                'payment_id' => $request->input('data.id'),
                'live_mode' => $request->input('live_mode'),
                'headers' => [
                    'content-type' => $request->header('content-type'),
                    'x-signature' => $request->header('x-signature'),
                    'x-request-id' => $request->header('x-request-id'),
                ],
                'body' => $request->all()
            ]);

            // Validar assinatura do webhook (se configurada)
            if (!$this->validateWebhookSignature($request, $requestId)) {
                Log::warning('⚠️ [WEBHOOK] Assinatura inválida', [
                    'request_id' => $requestId,
                    'ip' => $request->ip(),
                    'x_signature' => $request->header('x-signature')
                ]);

                return response()->json(['error' => 'Invalid signature'], 401);
            }

            // Verificar se é uma notificação de pagamento
            if ($request->input('type') === 'payment') {
                $paymentId = $request->input('data.id');

                if ($paymentId) {
                    Log::info('💳 [WEBHOOK] Processando pagamento', [
                        'request_id' => $requestId,
                        'payment_id' => $paymentId,
                        'action' => $request->input('action')
                    ]);

                    $this->processPaymentNotification($paymentId, $requestId);
                } else {
                    Log::warning('⚠️ [WEBHOOK] Payment ID não encontrado', [
                        'request_id' => $requestId,
                        'body' => $request->all()
                    ]);
                }
            } else {
                Log::info('ℹ️ [WEBHOOK] Tipo de notificação ignorado', [
                    'request_id' => $requestId,
                    'type' => $request->input('type'),
                    'supported_types' => ['payment']
                ]);
            }

            $processingTime = round((microtime(true) - $startTime) * 1000, 2);

            Log::info('✅ [WEBHOOK] Processamento concluído', [
                'request_id' => $requestId,
                'processing_time_ms' => $processingTime,
                'status' => 'success'
            ]);

            return response()->json([
                'status' => 'ok',
                'request_id' => $requestId,
                'processing_time_ms' => $processingTime
            ], 200);

        } catch (\Exception $e) {
            $processingTime = round((microtime(true) - $startTime) * 1000, 2);

            Log::error('❌ [WEBHOOK] Erro ao processar webhook Mercado Pago', [
                'request_id' => $requestId,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
                'processing_time_ms' => $processingTime,
                'request_data' => $request->all()
            ]);

            return response()->json([
                'error' => 'Internal server error',
                'request_id' => $requestId
            ], 500);
        }
    }

    /**
     * Process payment notification from Mercado Pago
     */
    private function processPaymentNotification($paymentId, $requestId = null)
    {
        try {
            Log::info('🔍 [WEBHOOK] Buscando informações do pagamento no MP', [
                'request_id' => $requestId,
                'payment_id' => $paymentId
            ]);

            // Buscar informações do pagamento no Mercado Pago
            $paymentInfo = $this->mercadoPagoService->getPayment($paymentId);

            if (!$paymentInfo) {
                Log::warning('⚠️ [WEBHOOK] Pagamento não encontrado no Mercado Pago', [
                    'request_id' => $requestId,
                    'payment_id' => $paymentId
                ]);
                return;
            }

            Log::info('📄 [WEBHOOK] Informações do pagamento obtidas', [
                'request_id' => $requestId,
                'payment_id' => $paymentId,
                'status' => $paymentInfo['status'] ?? 'unknown',
                'external_reference' => $paymentInfo['external_reference'] ?? null,
                'payment_method' => $paymentInfo['payment_method_id'] ?? null,
                'amount' => $paymentInfo['transaction_amount'] ?? null
            ]);

            $externalReference = $paymentInfo['external_reference'] ?? null;
            $status = $paymentInfo['status'] ?? null;

            if (!$externalReference) {
                Log::warning('⚠️ [WEBHOOK] External reference não encontrado no pagamento', [
                    'request_id' => $requestId,
                    'payment_id' => $paymentId,
                    'payment_info_keys' => array_keys($paymentInfo)
                ]);
                return;
            }

            Log::info('🔍 [WEBHOOK] Buscando pagamento no banco de dados', [
                'request_id' => $requestId,
                'external_reference' => $externalReference,
                'mp_status' => $status
            ]);

            // Buscar o pagamento no banco de dados
            $pagamento = Pagamento::where('id', $externalReference)->first();

            if (!$pagamento) {
                Log::warning('⚠️ [WEBHOOK] Pagamento não encontrado no banco de dados', [
                    'request_id' => $requestId,
                    'external_reference' => $externalReference,
                    'payment_id' => $paymentId
                ]);
                return;
            }

            // Mapear status do Mercado Pago para status interno
            $newStatus = $this->mapMercadoPagoStatus($status);

            Log::info('🔄 [WEBHOOK] Status mapeado', [
                'request_id' => $requestId,
                'pagamento_id' => $pagamento->id,
                'old_status' => $pagamento->status,
                'mp_status' => $status,
                'new_status' => $newStatus
            ]);

            // Atualizar o pagamento se o status mudou
            if ($pagamento->status !== $newStatus) {
                $updateData = [
                    'status' => $newStatus,
                    'transaction_id' => $paymentId,
                    'gateway_response' => $paymentInfo,
                ];

                // Se foi aprovado, definir data de pagamento
                if ($newStatus === 'pago') {
                    $updateData['paid_at'] = Carbon::now();
                    $updateData['method'] = $this->mapPaymentMethod($paymentInfo['payment_method_id'] ?? null);

                    Log::info('💰 [WEBHOOK] Pagamento aprovado', [
                        'request_id' => $requestId,
                        'pagamento_id' => $pagamento->id,
                        'amount' => $paymentInfo['transaction_amount'] ?? null,
                        'payment_method' => $updateData['method']
                    ]);
                }

                $pagamento->update($updateData);

                Log::info('✅ [WEBHOOK] Pagamento atualizado com sucesso', [
                    'request_id' => $requestId,
                    'pagamento_id' => $pagamento->id,
                    'old_status' => $pagamento->getOriginal('status'),
                    'new_status' => $newStatus,
                    'payment_id' => $paymentId,
                    'user_id' => $pagamento->assinatura->user_id ?? null,
                    'plano_id' => $pagamento->assinatura->plano_id ?? null
                ]);

                // Processar ações adicionais baseadas no status
                $this->handlePaymentStatusChange($pagamento, $newStatus, $requestId);
            } else {
                Log::info('ℹ️ [WEBHOOK] Status do pagamento inalterado', [
                    'request_id' => $requestId,
                    'pagamento_id' => $pagamento->id,
                    'current_status' => $pagamento->status,
                    'mp_status' => $status
                ]);
            }

        } catch (\Exception $e) {
            Log::error('❌ [WEBHOOK] Erro ao processar notificação de pagamento', [
                'request_id' => $requestId,
                'payment_id' => $paymentId,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Validate webhook signature from Mercado Pago
     */
    private function validateWebhookSignature(Request $request, $requestId = null)
    {
        // Obter assinatura secreta do ambiente
        $webhookSecret = config('services.mercadopago.webhook_secret');

        // Se não há secret configurado, pular validação (modo desenvolvimento)
        if (empty($webhookSecret)) {
            Log::info('ℹ️ [WEBHOOK] Validação de assinatura desabilitada (webhook_secret não configurado)', [
                'request_id' => $requestId
            ]);
            return true;
        }

        $xSignature = $request->header('x-signature');
        $xRequestId = $request->header('x-request-id');

        if (empty($xSignature)) {
            Log::warning('⚠️ [WEBHOOK] Header x-signature não encontrado', [
                'request_id' => $requestId
            ]);
            return false;
        }

        // Extrair timestamp e hash da assinatura
        // Formato: ts=1234567890,v1=hash_value
        $signatureParts = [];
        foreach (explode(',', $xSignature) as $part) {
            $keyValue = explode('=', $part, 2);
            if (count($keyValue) === 2) {
                $signatureParts[$keyValue[0]] = $keyValue[1];
            }
        }

        if (!isset($signatureParts['ts']) || !isset($signatureParts['v1'])) {
            Log::warning('⚠️ [WEBHOOK] Formato de assinatura inválido', [
                'request_id' => $requestId,
                'x_signature' => $xSignature
            ]);
            return false;
        }

        $timestamp = $signatureParts['ts'];
        $receivedHash = $signatureParts['v1'];

        // Verificar se o timestamp não é muito antigo (5 minutos)
        $currentTime = time();
        if (abs($currentTime - $timestamp) > 300) {
            Log::warning('⚠️ [WEBHOOK] Timestamp muito antigo', [
                'request_id' => $requestId,
                'timestamp' => $timestamp,
                'current_time' => $currentTime,
                'diff_seconds' => abs($currentTime - $timestamp)
            ]);
            return false;
        }

        // Construir string para validação
        $dataId = $request->input('data.id', '');
        $stringToSign = "id:{$dataId};request-id:{$xRequestId};ts:{$timestamp};";

        // Calcular hash esperado
        $expectedHash = hash_hmac('sha256', $stringToSign, $webhookSecret);

        // Comparar hashes
        $isValid = hash_equals($expectedHash, $receivedHash);

        Log::info('🔐 [WEBHOOK] Validação de assinatura', [
            'request_id' => $requestId,
            'is_valid' => $isValid,
            'timestamp' => $timestamp,
            'data_id' => $dataId,
            'x_request_id' => $xRequestId,
            'string_to_sign' => $stringToSign,
            'expected_hash' => $expectedHash,
            'received_hash' => $receivedHash
        ]);

        return $isValid;
    }

    /**
     * Map Mercado Pago status to internal status
     */
    private function mapMercadoPagoStatus($mpStatus)
    {
        switch ($mpStatus) {
            case 'approved':
                return 'pago';
            case 'pending':
            case 'in_process':
                return 'pendente';
            case 'rejected':
            case 'cancelled':
                return 'falhou';
            default:
                return 'pendente';
        }
    }

    /**
     * Map Mercado Pago payment method to internal method
     */
    private function mapPaymentMethod($mpMethod)
    {
        if (!$mpMethod) return null;

        // Mapear métodos do MP para métodos internos
        $methodMap = [
            'pix' => 'pix',
            'bolbradesco' => 'boleto',
            'visa' => 'cartao_credito',
            'master' => 'cartao_credito',
            'amex' => 'cartao_credito',
            'elo' => 'cartao_credito',
            'hipercard' => 'cartao_credito',
            'debvisa' => 'cartao_debito',
            'debmaster' => 'cartao_debito',
        ];

        return $methodMap[$mpMethod] ?? 'cartao_credito';
    }

    /**
     * Handle additional actions when payment status changes
     */
    private function handlePaymentStatusChange(Pagamento $pagamento, $newStatus, $requestId = null)
    {
        if ($newStatus === 'pago') {
            // Pagamento aprovado - ações adicionais
            Log::info('🎉 [WEBHOOK] Pagamento aprovado - executando ações adicionais', [
                'request_id' => $requestId,
                'pagamento_id' => $pagamento->id,
                'assinatura_id' => $pagamento->assinatura_id,
                'amount' => $pagamento->amount,
                'user_id' => $pagamento->assinatura->user_id ?? null
            ]);

            // Confirmar venda de afiliado se existir
            $this->confirmAffiliateCommission($pagamento, $requestId);

            // Aqui você pode adicionar:
            // - Ativar/renovar assinatura
            // - Enviar email de confirmação
            // - Criar notificação para o usuário
            // - Atualizar status da assinatura se necessário

        } elseif ($newStatus === 'falhou') {
            Log::info('❌ [WEBHOOK] Pagamento falhou', [
                'request_id' => $requestId,
                'pagamento_id' => $pagamento->id,
                'assinatura_id' => $pagamento->assinatura_id,
                'user_id' => $pagamento->assinatura->user_id ?? null
            ]);
        }
    }

    /**
     * Confirm affiliate commission when payment is approved
     */
    private function confirmAffiliateCommission(Pagamento $pagamento, $requestId = null)
    {
        try {
            Log::info('🤝 [WEBHOOK] Verificando comissão de afiliado', [
                'request_id' => $requestId,
                'pagamento_id' => $pagamento->id,
                'transaction_id' => $pagamento->transaction_id,
                'assinatura_id' => $pagamento->assinatura_id
            ]);

            // Buscar venda de afiliado relacionada a este transaction_id
            $vendaAfiliado = null;

            // Primeiro, tentar buscar por transaction_id (mais preciso)
            if ($pagamento->transaction_id) {
                $vendaAfiliado = VendaAfiliado::where('transaction_id', $pagamento->transaction_id)
                    ->where('status', 'pendente')
                    ->first();
            }

            // Se não encontrou por transaction_id, buscar por assinatura_id (fallback)
            if (!$vendaAfiliado) {
                $vendaAfiliado = VendaAfiliado::where('assinatura_id', $pagamento->assinatura_id)
                    ->where('status', 'pendente')
                    ->whereNull('transaction_id') // Apenas vendas sem transaction_id específico
                    ->first();
            }

            if ($vendaAfiliado) {
                // Confirmar a venda de afiliado
                $vendaAfiliado->confirmar('Pagamento confirmado via webhook - Transaction ID: ' . $pagamento->transaction_id);

                Log::info('💰 [WEBHOOK] Comissão de afiliado confirmada', [
                    'request_id' => $requestId,
                    'venda_afiliado_id' => $vendaAfiliado->id,
                    'afiliado_id' => $vendaAfiliado->afiliado_id,
                    'pagamento_id' => $pagamento->id,
                    'transaction_id' => $pagamento->transaction_id,
                    'comissao' => $vendaAfiliado->comissao,
                    'valor_venda' => $vendaAfiliado->valor_venda,
                ]);

                // Atualizar estatísticas do afiliado
                $affiliateService = new AffiliateTrackingService();
                $affiliateService->updateAffiliateStats($vendaAfiliado->afiliado);

            } else {
                Log::info('ℹ️ [WEBHOOK] Nenhuma venda de afiliado encontrada para confirmação', [
                    'request_id' => $requestId,
                    'pagamento_id' => $pagamento->id,
                    'assinatura_id' => $pagamento->assinatura_id,
                    'transaction_id' => $pagamento->transaction_id,
                ]);
            }

        } catch (\Exception $e) {
            Log::error('❌ [WEBHOOK] Erro ao confirmar comissão de afiliado', [
                'request_id' => $requestId,
                'pagamento_id' => $pagamento->id,
                'transaction_id' => $pagamento->transaction_id,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }
}
