<?php

namespace App\Http\Controllers\Paciente;

use App\Http\Controllers\Controller;
use App\Models\Plano;
use App\Models\Assinatura;
use App\Services\AffiliateTrackingService;
use App\Services\MercadoPagoService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;

class PlanosController extends Controller
{
    /**
     * Exibe a página de seleção de planos
     */
    public function index()
    {
        $planos = [
            [
                'id' => 'plano-busca',
                'name' => 'Plano Busca',
                'price' => 14.80,
                'description' => 'Encontre fisioterapeutas, farmácias e dentistas próximos a você',
                'features' => [
                    'Busca ilimitada de profissionais',
                    'Filtros avançados por especialidade',
                    'Visualização de avaliações',
                    'Informações de contato',
                    'Localização no mapa',
                    'Horários de funcionamento',
                    'Suporte por chat',
                ],
                'type' => 'busca'
            ],
            [
                'id' => 'plano-pessoal',
                'name' => 'Plano Pessoal',
                'price' => 180.00,
                'description' => 'Atendimento fisioterapêutico domiciliar personalizado',
                'features' => [
                    'Todos os recursos do Plano Busca',
                    'Agendamento de consultas domiciliares',
                    'Fisioterapeutas qualificados',
                    'Atendimento personalizado',
                    'Relatórios de avaliação',
                    'Suporte telefônico',
                    'Cancelamento flexível',
                    'Histórico médico digital',
                ],
                'type' => 'pessoal',
                'popular' => true
            ],
            [
                'id' => 'plano-empresarial',
                'name' => 'Plano Empresarial',
                'price' => 640.00,
                'description' => 'Solução completa de fisioterapia para sua empresa',
                'features' => [
                    'Todos os recursos do Plano Pessoal',
                    'Atendimento para até 20 funcionários',
                    'Gestão centralizada',
                    'Relatórios empresariais',
                    'Programa de prevenção',
                    'Treinamentos ergonômicos',
                    'Descontos em consultas extras',
                    'Dashboard administrativo',
                ],
                'type' => 'empresarial'
            ]
        ];

        $user = auth()->user();

        // Verificar se o usuário já tem um plano selecionado
        $planoAtual = null;
        if ($user->has_subscription) {
            $assinatura = \App\Models\Assinatura::where('user_id', $user->id)
                ->where('status', 'ativa')
                ->first();

            if ($assinatura) {
                // Determinar o tipo de plano baseado no preço
                $planoType = 'pessoal'; // default
                if ($assinatura->monthly_price == 14.80) {
                    $planoType = 'busca';
                    $planoName = 'Plano Busca';
                } elseif ($assinatura->monthly_price == 180.00) {
                    $planoType = 'pessoal';
                    $planoName = 'Plano Pessoal';
                } elseif ($assinatura->monthly_price == 640.00) {
                    $planoType = 'empresarial';
                    $planoName = 'Plano Empresarial';
                }

                $planoAtual = [
                    'type' => $planoType,
                    'name' => $planoName,
                    'status' => $assinatura->status
                ];
            }
        }

        return Inertia::render('paciente/planos', [
            'planos' => $planos,
            'planoAtual' => $planoAtual,
            'hasSubscription' => $user->has_subscription
        ]);
    }

    /**
     * Processa a seleção do plano
     */
    public function store(Request $request)
    {
        \Log::info('PlanosController@store called', [
            'user' => auth()->user(),
            'request_data' => $request->all()
        ]);

        $request->validate([
            'plano_type' => 'required|string|in:busca,pessoal,empresarial'
        ]);

        $user = auth()->user();
        $planoType = $request->plano_type;

        \Log::info('Processing plan selection', [
            'user_id' => $user->id,
            'plano_type' => $planoType,
            'current_has_subscription' => $user->has_subscription
        ]);

        if ($planoType === 'empresarial') {
            // Para plano empresarial, redireciona para WhatsApp
            return redirect()->away(
                'https://wa.me/5511978196207?text=' . urlencode(
                    'Olá! Gostaria de saber mais sobre o plano empresarial de fisioterapia domiciliar para grupos/empresas. Meu nome é ' . $user->name . ' e meu email é ' . $user->email . '.'
                )
            );
        }

        if ($planoType === 'busca') {
            // Se já tem assinatura, cancelar primeiro
            if ($user->has_subscription) {
                $assinaturaAtiva = \App\Models\Assinatura::where('user_id', $user->id)
                    ->where('status', 'ativa')
                    ->first();

                if ($assinaturaAtiva) {
                    $assinaturaAtiva->update(['status' => 'cancelada']);
                    \Log::info('Cancelled previous subscription for plan change', [
                        'user_id' => $user->id,
                        'old_subscription_id' => $assinaturaAtiva->id
                    ]);
                }
            }

            // Para plano busca, criar assinatura
            $user->update(['has_subscription' => true]);

            $assinatura = \App\Models\Assinatura::create([
                'user_id' => $user->id,
                'plano_id' => null,
                'status' => 'ativa',
                'start_date' => \Carbon\Carbon::now(),
                'end_date' => \Carbon\Carbon::now()->addMonth(),
                'sessions_used' => 0,
                'current_period_start' => \Carbon\Carbon::now(),
                'current_period_end' => \Carbon\Carbon::now()->addMonth(),
                'monthly_price' => 14.80,
            ]);

            \Log::info('Created new busca subscription', [
                'user_id' => $user->id,
                'subscription_id' => $assinatura->id
            ]);

            return redirect()->route('paciente.planos')
                ->with('success', 'Plano Busca selecionado com sucesso! Agora você pode buscar profissionais próximos a você.');
        }

        // Para plano pessoal, cancelar assinatura anterior se existir
        if ($user->has_subscription) {
            $assinaturaAtiva = \App\Models\Assinatura::where('user_id', $user->id)
                ->where('status', 'ativa')
                ->first();

            if ($assinaturaAtiva) {
                $assinaturaAtiva->update(['status' => 'cancelada']);
                \Log::info('Cancelled previous subscription for plan change', [
                    'user_id' => $user->id,
                    'old_subscription_id' => $assinaturaAtiva->id
                ]);
            }
        }

        // Determinar preço baseado no tipo de plano
        $monthlyPrice = 180.00; // Plano Pessoal por padrão
        if ($planoType === 'empresarial') {
            $monthlyPrice = 640.00;
        }

        // Criar assinatura
        $user->update(['has_subscription' => true]);

        $assinatura = Assinatura::create([
            'user_id' => $user->id,
            'plano_id' => null, // Será definido após confirmação
            'status' => 'ativa', // Ativo para teste, em produção seria 'pendente'
            'start_date' => Carbon::now(),
            'end_date' => Carbon::now()->addMonth(),
            'sessions_used' => 0,
            'current_period_start' => Carbon::now(),
            'current_period_end' => Carbon::now()->addMonth(),
            'monthly_price' => $monthlyPrice,
        ]);

        \Log::info('Created new subscription', [
            'user_id' => $user->id,
            'subscription_id' => $assinatura->id,
            'plan_type' => $planoType,
            'monthly_price' => $monthlyPrice
        ]);

        // Processar venda de afiliado se houver referência
        $affiliateService = new AffiliateTrackingService();
        $affiliateService->createAffiliateSale(
            $user,
            $assinatura->id,
            $planoType,
            $monthlyPrice,
            $request
        );

        // Redirecionar para pagamento via Mercado Pago
        // Por enquanto, vamos apenas confirmar a seleção
        $planName = $planoType === 'pessoal' ? 'Plano Pessoal' : 'Plano Empresarial';
        return redirect()->route('paciente.planos')
            ->with('success', $planName . ' selecionado! Em breve você será redirecionado para o pagamento via Mercado Pago.');
    }

    /**
     * Retorna dados do plano baseado no tipo
     */
    private function getPlanoData($type)
    {
        $planos = [
            'busca' => ['price' => 14.80, 'sessions' => 0],
            'pessoal' => ['price' => 180.00, 'sessions' => 1],
            'empresarial' => ['price' => 640.00, 'sessions' => 4],
        ];

        return $planos[$type] ?? ['price' => 0, 'sessions' => 0];
    }

    /**
     * Verificar se o Mercado Pago está configurado
     */
    public function checkMercadoPagoStatus()
    {
        $mercadoPagoService = new MercadoPagoService();

        // Verificar se está em modo simulação ou se tem credenciais configuradas
        $isConfigured = config('services.mercadopago.access_token') ||
                       config('services.mercadopago.simulate', false) ||
                       config('app.env') === 'local';

        return response()->json([
            'configured' => $isConfigured,
            'sandbox_mode' => config('services.mercadopago.sandbox', true),
            'simulation_mode' => config('services.mercadopago.simulate', false) || config('app.env') === 'local'
        ]);
    }
}
