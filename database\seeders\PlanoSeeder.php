<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Plano;

class PlanoSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $planos = [
            [
                'name' => 'Plano Busca',
                'description' => 'Encontre fisioterapeutas, farmácias e dentistas próximos a você',
                'price' => 14.80,
                'sessions_per_month' => 0,
                'session_duration' => 0,
                'included_services' => [
                    'Busca ilimitada de profissionais',
                    'Filtros avançados por especialidade',
                    'Visualização de avaliações',
                    'Informações de contato',
                    'Localização no mapa',
                    'Horários de funcionamento',
                    'Suporte por chat'
                ],
                'benefits' => [
                    'Busca ilimitada de profissionais',
                    'Filtros avançados',
                    'Suporte por chat'
                ],
                'active' => true,
            ],
            [
                'name' => 'Plano Pessoal',
                'description' => 'Atendimento fisioterapêutico domiciliar personalizado',
                'price' => 180.00,
                'sessions_per_month' => 1,
                'session_duration' => 60,
                'included_services' => [
                    'Todos os recursos do Plano Busca',
                    'Agendamento de consultas domiciliares',
                    'Fisioterapeutas qualificados',
                    'Atendimento personalizado',
                    'Relatórios de avaliação',
                    'Suporte telefônico',
                    'Cancelamento flexível',
                    'Histórico médico digital'
                ],
                'benefits' => [
                    'Atendimento domiciliar',
                    'Fisioterapeutas qualificados',
                    'Relatórios de avaliação',
                    'Suporte telefônico'
                ],
                'active' => true,
            ],
            [
                'name' => 'Plano Empresarial',
                'description' => 'Solução completa de fisioterapia para sua empresa',
                'price' => 640.00,
                'sessions_per_month' => 4,
                'session_duration' => 60,
                'included_services' => [
                    'Todos os recursos do Plano Pessoal',
                    'Atendimento para até 20 funcionários',
                    'Gestão centralizada',
                    'Relatórios empresariais',
                    'Programa de prevenção',
                    'Treinamentos ergonômicos',
                    'Descontos em consultas extras',
                    'Dashboard administrativo'
                ],
                'benefits' => [
                    'Atendimento empresarial',
                    'Gestão centralizada',
                    'Relatórios empresariais',
                    'Dashboard administrativo'
                ],
                'active' => true,
            ],
        ];

        foreach ($planos as $plano) {
            Plano::create($plano);
        }
    }
}
