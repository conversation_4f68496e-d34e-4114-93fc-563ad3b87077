import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Link } from '@inertiajs/react';
import { Check } from 'lucide-react';

export default function PricingSection() {
    return (
        <section id="planos" className="relative bg-muted/30 py-20">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                <div className="mx-auto max-w-2xl text-center">
                    <h2 className="text-3xl font-medium text-balance md:text-4xl">Planos que se adaptam às suas necessidades</h2>
                    <p className="mx-auto mt-8 max-w-2xl text-xl text-balance text-muted-foreground">
                        Escolha o plano ideal para seu tratamento de fisioterapia domiciliar. Todos incluem avaliação gratuita.
                    </p>
                </div>
                <div className="@container relative mt-12 md:mt-20">
                    <Card className="relative mx-auto max-w-sm @4xl:max-w-full">
                        <div className="grid gap-0 @4xl:min-h-[600px] @4xl:grid-cols-3">
                            {/* Plano Busca */}
                            <div className="flex flex-col @4xl:min-h-full">
                                <CardHeader className="flex-shrink-0 p-8">
                                    <CardTitle className="font-medium">Plano Busca</CardTitle>
                                    <span className="mt-2 mb-0.5 block text-2xl font-semibold">R$ 14,80</span>
                                    <CardDescription className="text-sm">Por mês</CardDescription>
                                </CardHeader>

                                <ul role="list" className="flex-grow space-y-3 px-8">
                                    {[
                                        'Busca ilimitada de profissionais',
                                        'Filtros avançados por especialidade',
                                        'Visualização de avaliações',
                                        'Informações de contato',
                                        'Localização no mapa',
                                        'Horários de funcionamento',
                                        'Suporte por chat',
                                    ].map((item, index) => (
                                        <li key={index} className="flex items-center gap-2">
                                            <Check className="size-3 text-primary" strokeWidth={3.5} />
                                            <span className="text-sm">{item}</span>
                                        </li>
                                    ))}
                                </ul>

                                <div className="mt-auto border-t px-8 py-4">
                                    <Button asChild className="w-full" variant="outline">
                                        <a
                                            href="/planos-publicos"
                                        >
                                            Começar Busca
                                        </a>
                                    </Button>
                                </div>
                            </div>

                            {/* Plano Pessoal - Destacado */}
                            <div className="-mx-1 rounded-(--radius) border-transparent bg-background shadow ring-1 ring-foreground/10 @3xl:mx-0 @3xl:-my-3 @4xl:min-h-full">
                                <div className="relative flex flex-col px-1 @3xl:px-0 @3xl:py-3 @4xl:min-h-full">
                                    <div className="absolute -top-4 left-1/2 -translate-x-1/2 rounded-full bg-primary px-4 py-1 text-sm font-semibold text-primary-foreground">
                                        Mais Popular
                                    </div>
                                    <CardHeader className="flex-shrink-0 p-8">
                                        <CardTitle className="font-medium">Plano Pessoal</CardTitle>
                                        <span className="mt-2 mb-0.5 block text-2xl font-semibold">R$ 180</span>
                                        <CardDescription className="text-sm">Por mês</CardDescription>
                                        <div className="flex items-center gap-2 text-sm">
                                            <span className="text-primary font-medium">
                                                Atendimento fisioterapêutico domiciliar personalizado
                                            </span>
                                        </div>
                                    </CardHeader>

                                    <ul role="list" className="flex-grow space-y-3 px-8">
                                        {[
                                            'Todos os recursos do Plano Busca',
                                            'Agendamento de consultas domiciliares',
                                            'Fisioterapeutas qualificados',
                                            'Atendimento personalizado',
                                            'Relatórios de avaliação',
                                            'Suporte telefônico',
                                            'Cancelamento flexível',
                                            'Histórico médico digital',
                                        ].map((item, index) => (
                                            <li key={index} className="flex items-center gap-2">
                                                <Check className="size-3 text-primary" strokeWidth={3.5} />
                                                <span className="text-sm">{item}</span>
                                            </li>
                                        ))}
                                    </ul>

                                    <div className="-mx-1 mt-auto border-t px-8 py-4 @3xl:mx-0">
                                        <Button asChild className="w-full">
                                            <a
                                                href="/planos-publicos"
                                            >
                                                Contratar Plano
                                            </a>
                                        </Button>
                                    </div>
                                </div>
                            </div>

                            {/* Plano Empresarial/Grupos */}
                            <div className="flex flex-col @4xl:min-h-full">
                                <CardHeader className="flex-shrink-0 p-8">
                                    <CardTitle className="font-medium">Plano Empresarial</CardTitle>
                                    <span className="mt-2 mb-0.5 block text-2xl font-semibold">R$ 640</span>
                                    <CardDescription className="text-sm">Por mês</CardDescription>
                                    <div className="flex items-center gap-2 text-sm">
                                        <span className="text-primary font-medium">
                                            Solução completa de fisioterapia para sua empresa
                                        </span>
                                    </div>
                                </CardHeader>

                                <ul role="list" className="flex-grow space-y-3 px-8">
                                    {[
                                        'Todos os recursos do Plano Pessoal',
                                        'Atendimento para até 20 funcionários',
                                        'Gestão centralizada',
                                        'Relatórios empresariais',
                                        'Programa de prevenção',
                                        'Treinamentos ergonômicos',
                                        'Descontos em consultas extras',
                                        'Dashboard administrativo',
                                    ].map((item, index) => (
                                        <li key={index} className="flex items-center gap-2">
                                            <Check className="size-3 text-primary" strokeWidth={3.5} />
                                            <span className="text-sm">{item}</span>
                                        </li>
                                    ))}
                                </ul>

                                <div className="mt-auto border-t px-8 py-4">
                                    <Button asChild className="w-full" variant="outline">
                                        <a
                                            href="/planos-publicos"
                                        >
                                            Solicitar Proposta
                                        </a>
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </Card>
                </div>

                {/* Botão Ver Todos os Planos */}
                <div className="mt-12 text-center">
                    <Button asChild variant="outline" size="lg">
                        <Link href="/planos">Ver Todos os Planos</Link>
                    </Button>
                </div>
            </div>
        </section>
    );
}
