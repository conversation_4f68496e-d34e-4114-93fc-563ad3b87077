import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Card, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, useForm } from '@inertiajs/react';
import { Check, MessageCircle } from 'lucide-react';
import { useEffect, useState } from 'react';

interface Plano {
    id: string;
    name: string;
    price: number | null;
    original_price?: number;
    description: string;
    savings?: number;
    features: string[];
    type: string;
    popular?: boolean;
}

interface PlanoAtual {
    type: string;
    name: string;
    status: string;
}

interface PlanosProps {
    planos: Plano[];
    planoAtual?: PlanoAtual;
    hasSubscription: boolean;
}

export default function Planos({ planos, planoAtual, hasSubscription }: PlanosProps) {
    const { data, setData, post, processing } = useForm({
        plano_type: '',
    });

    const [showMercadoPagoDialog, setShowMercadoPagoDialog] = useState(false);
    const [mercadoPagoStatus, setMercadoPagoStatus] = useState<{
        configured: boolean;
        sandbox_mode: boolean;
        simulation_mode: boolean;
    } | null>(null);

    // Verificar status do Mercado Pago ao carregar a página
    useEffect(() => {
        fetch(route('paciente.planos.mercadopago-status'))
            .then((response) => response.json())
            .then((data) => setMercadoPagoStatus(data))
            .catch((error) => console.error('Erro ao verificar status do Mercado Pago:', error));
    }, []);

    const handleSelectPlan = (planoType: string) => {
        if (planoType === 'busca') {
            setData('plano_type', 'busca');
            post(route('paciente.planos.store'));
        } else if (planoType === 'pessoal') {
            // Verificar se as APIs do Mercado Pago estão configuradas
            if (mercadoPagoStatus?.configured) {
                // Se configurado, processar diretamente
                setData('plano_type', 'pessoal');
                post(route('paciente.planos.store'));
            } else {
                // Se não configurado, mostrar dialog
                setShowMercadoPagoDialog(true);
            }
        } else if (planoType === 'empresarial') {
            // Redirecionar para WhatsApp
            const message = encodeURIComponent(
                'Olá! Gostaria de saber mais sobre o plano empresarial de fisioterapia domiciliar para grupos/empresas.',
            );
            window.open(`https://wa.me/5511978196207?text=${message}`, '_blank');
        }
    };

    const handleConfirmPessoal = () => {
        setData('plano_type', 'pessoal');
        post(route('paciente.planos.store'));
        setShowMercadoPagoDialog(false);
    };

    const handleMercadoPago = () => {
        // Simular que não tem APIs configuradas
        setShowMercadoPagoDialog(false);
        // Aqui seria a integração real com Mercado Pago
        // Por enquanto, apenas mostra o dialog de aviso
    };

    const formatPrice = (price: number | null) => {
        if (price === null) return 'Sob Consulta';
        return `R$ ${price.toFixed(2).replace('.', ',')}`;
    };

    const breadcrumbs = [
        {
            title: 'Início',
            href: '/paciente/dashboard',
        },
        {
            title: 'Escolher Plano',
            href: '/paciente/planos',
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Escolher Plano" />

            {/* Hero Section */}
            <section className="bg-gradient-to-b from-background to-muted/30 py-12 sm:py-8">
                <div className="relative">
                    <div className="relative z-10 mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="text-center">
                            <h1 className="mx-auto max-w-4xl text-3xl font-medium text-balance sm:text-4xl md:text-5xl">
                                Escolha seu <span className="text-primary">Plano</span>
                            </h1>
                            <p className="mx-auto my-6 max-w-3xl text-lg text-balance text-muted-foreground sm:my-8 sm:text-xl">
                                Selecione o plano que melhor atende às suas necessidades de fisioterapia domiciliar.
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* Plano Atual Section */}
            {hasSubscription && planoAtual && (
                <section className="bg-background py-12">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="mx-auto max-w-2xl">
                            <div className="rounded-lg border bg-card p-6 shadow-sm">
                                <div className="flex items-center gap-3">
                                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10">
                                        <Check className="h-5 w-5 text-primary" />
                                    </div>
                                    <div>
                                        <h3 className="text-lg font-semibold">Plano Atual</h3>
                                        <p className="text-sm text-muted-foreground">
                                            Você possui o plano <strong>{planoAtual.name}</strong> ativo.
                                            <br />
                                            Você pode trocar de plano a qualquer momento selecionando uma das opções abaixo.
                                        </p>
                                    </div>
                                </div>
                                <div className="mt-4 flex gap-2">
                                    <Button variant="outline" asChild>
                                        <Link href="/paciente/dashboard">Ir para Dashboard</Link>
                                    </Button>
                                    {planoAtual.type !== 'avulsa' && (
                                        <Button variant="outline" asChild>
                                            <Link href={route('paciente.plano.index')}>Gerenciar Plano</Link>
                                        </Button>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            )}

            {/* Pricing Section */}
            <div className="relative bg-muted/30 py-20">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="mx-auto max-w-2xl text-center">
                        <h2 className="text-3xl font-medium text-balance md:text-4xl">Planos que se adaptam às suas necessidades</h2>
                        <p className="mx-auto mt-8 max-w-2xl text-xl text-balance text-muted-foreground">
                            Escolha o plano ideal para seu tratamento de fisioterapia domiciliar. Todos incluem avaliação gratuita.
                        </p>
                    </div>
                    <div className="@container relative mt-12 md:mt-20">
                        <Card className="relative mx-auto max-w-sm @4xl:max-w-full">
                            <div className="grid gap-0 @4xl:min-h-[600px] @4xl:grid-cols-3">
                                {/* Plano Busca */}
                                <div className="flex flex-col @4xl:min-h-full">
                                    <CardHeader className="flex-shrink-0 p-8">
                                        <CardTitle className="font-medium">Plano Busca</CardTitle>
                                        <span className="mt-2 mb-0.5 block text-2xl font-semibold">R$ 14,80</span>
                                        <CardDescription className="text-sm">Por mês</CardDescription>
                                    </CardHeader>

                                    <ul role="list" className="flex-grow space-y-3 px-8">
                                        {[
                                            'Busca ilimitada de profissionais',
                                            'Filtros avançados por especialidade',
                                            'Visualização de avaliações',
                                            'Informações de contato',
                                            'Localização no mapa',
                                            'Horários de funcionamento',
                                            'Suporte por chat',
                                        ].map((item, index) => (
                                            <li key={index} className="flex items-center gap-2">
                                                <Check className="size-3 text-primary" strokeWidth={3.5} />
                                                <span className="text-sm">{item}</span>
                                            </li>
                                        ))}
                                    </ul>

                                    <div className="mt-auto border-t px-8 py-4">
                                        {hasSubscription && planoAtual?.type === 'busca' ? (
                                            <Button disabled className="w-full" variant="outline">
                                                <Check className="mr-2 h-4 w-4" />
                                                Plano Atual
                                            </Button>
                                        ) : (
                                            <Button
                                                onClick={() => handleSelectPlan('busca')}
                                                disabled={processing}
                                                className="w-full"
                                                variant="outline"
                                            >
                                                {processing ? 'Processando...' : hasSubscription ? 'Trocar para este Plano' : 'Começar Busca'}
                                            </Button>
                                        )}
                                    </div>
                                </div>

                                {/* Plano Pessoal - Destacado */}
                                <div className="-mx-1 rounded-(--radius) border-transparent bg-background shadow ring-1 ring-foreground/10 @3xl:mx-0 @3xl:-my-3 @4xl:min-h-full">
                                    <div className="relative flex flex-col px-1 @3xl:px-0 @3xl:py-3 @4xl:min-h-full">
                                        <div className="absolute -top-4 left-1/2 -translate-x-1/2 rounded-full bg-primary px-4 py-1 text-sm font-semibold text-primary-foreground">
                                            Mais Popular
                                        </div>
                                        <CardHeader className="flex-shrink-0 p-8">
                                            <CardTitle className="font-medium">Plano Pessoal</CardTitle>
                                            <span className="mt-2 mb-0.5 block text-2xl font-semibold">R$ 180</span>
                                            <CardDescription className="text-sm">Por mês</CardDescription>
                                            <div className="flex items-center gap-2 text-sm">
                                                <span className="font-medium text-primary">
                                                    Atendimento fisioterapêutico domiciliar personalizado
                                                </span>
                                            </div>
                                        </CardHeader>

                                        <ul role="list" className="flex-grow space-y-3 px-8">
                                            {[
                                                'Todos os recursos do Plano Busca',
                                                'Agendamento de consultas domiciliares',
                                                'Fisioterapeutas qualificados',
                                                'Atendimento personalizado',
                                                'Relatórios de avaliação',
                                                'Suporte telefônico',
                                                'Cancelamento flexível',
                                                'Histórico médico digital',
                                            ].map((item, index) => (
                                                <li key={index} className="flex items-center gap-2">
                                                    <Check className="size-3 text-primary" strokeWidth={3.5} />
                                                    <span className="text-sm">{item}</span>
                                                </li>
                                            ))}
                                        </ul>

                                        <div className="-mx-1 mt-auto border-t px-8 py-4 @3xl:mx-0">
                                            {hasSubscription && planoAtual?.type === 'pessoal' ? (
                                                <Button disabled className="w-full">
                                                    <Check className="mr-2 h-4 w-4" />
                                                    Plano Atual
                                                </Button>
                                            ) : (
                                                <Button onClick={() => handleSelectPlan('pessoal')} disabled={processing} className="w-full">
                                                    {processing ? 'Processando...' : hasSubscription ? 'Trocar para este Plano' : 'Contratar Plano'}
                                                </Button>
                                            )}
                                        </div>
                                    </div>
                                </div>

                                {/* Plano Empresarial/Grupos */}
                                <div className="flex flex-col @4xl:min-h-full">
                                    <CardHeader className="flex-shrink-0 p-8">
                                        <CardTitle className="font-medium">Plano Empresarial</CardTitle>
                                        <span className="mt-2 mb-0.5 block text-2xl font-semibold">R$ 640</span>
                                        <CardDescription className="text-sm">Por mês</CardDescription>
                                        <div className="flex items-center gap-2 text-sm">
                                            <span className="font-medium text-primary">Solução completa de fisioterapia para sua empresa</span>
                                        </div>
                                    </CardHeader>

                                    <ul role="list" className="flex-grow space-y-3 px-8">
                                        {[
                                            'Todos os recursos do Plano Pessoal',
                                            'Atendimento para até 20 funcionários',
                                            'Gestão centralizada',
                                            'Relatórios empresariais',
                                            'Programa de prevenção',
                                            'Treinamentos ergonômicos',
                                            'Descontos em consultas extras',
                                            'Dashboard administrativo',
                                        ].map((item, index) => (
                                            <li key={index} className="flex items-center gap-2">
                                                <Check className="size-3 text-primary" strokeWidth={3.5} />
                                                <span className="text-sm">{item}</span>
                                            </li>
                                        ))}
                                    </ul>

                                    <div className="mt-auto border-t px-8 py-4">
                                        {hasSubscription && planoAtual?.type === 'empresarial' ? (
                                            <Button disabled className="w-full" variant="outline">
                                                <Check className="mr-2 h-4 w-4" />
                                                Plano Atual
                                            </Button>
                                        ) : (
                                            <Button
                                                onClick={() => handleSelectPlan('empresarial')}
                                                disabled={processing}
                                                className="w-full"
                                                variant="outline"
                                            >
                                                {processing ? 'Processando...' : 'Entrar em Contato'}
                                            </Button>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </Card>
                    </div>

                    <div className="mt-12 text-center">
                        <p className="text-sm text-muted-foreground">
                            Tem dúvidas sobre qual plano escolher?
                            <a
                                href="https://wa.me/5511978196207?text=Olá! Tenho dúvidas sobre os planos de fisioterapia domiciliar."
                                target="_blank"
                                rel="noopener noreferrer"
                                className="ml-1 text-primary hover:underline"
                            >
                                Entre em contato conosco
                            </a>
                        </p>
                    </div>
                </div>
            </div>

            {/* Dialog de Confirmação para Plano Pessoal */}
            <AlertDialog open={showMercadoPagoDialog} onOpenChange={setShowMercadoPagoDialog}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Confirmar Plano Pessoal</AlertDialogTitle>
                        <AlertDialogDescription>
                            Você está escolhendo o Plano Pessoal por R$ 180,00/mês.
                            <br />
                            <br />
                            Este plano inclui atendimento fisioterapêutico domiciliar personalizado com todos os recursos do Plano Busca, agendamento
                            de consultas domiciliares, fisioterapeutas qualificados e muito mais.
                            <br />
                            <br />
                            {mercadoPagoStatus?.configured
                                ? 'Você será redirecionado para o pagamento via Mercado Pago.'
                                : 'As APIs do Mercado Pago ainda não foram configuradas. Entre em contato conosco via WhatsApp para finalizar sua assinatura.'}
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancelar</AlertDialogCancel>
                        <Button
                            variant="outline"
                            onClick={() => {
                                const message = encodeURIComponent(
                                    'Olá! Gostaria de assinar o Plano Pessoal de fisioterapia domiciliar por R$ 180,00/mês.',
                                );
                                window.open(`https://wa.me/5511978196207?text=${message}`, '_blank');
                                setShowMercadoPagoDialog(false);
                            }}
                        >
                            <MessageCircle className="mr-2 h-4 w-4" />
                            Contatar via WhatsApp
                        </Button>
                        <AlertDialogAction onClick={handleConfirmPessoal}>Confirmar Plano</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </AppLayout>
    );
}
